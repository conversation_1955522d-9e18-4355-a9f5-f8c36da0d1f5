"""
A simple AI chatbot using FastAPI, OpenAI API, and SQLite for chat storage.
Make sure to install: fastapi, openai, sqlalchemy, uvicorn
"""

from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from sqlalchemy import create_engine, Column, Integer, String, Text, DateTime
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from datetime import datetime
import openai
import os

# Set your OpenAI API key here or use an environment variable
openai.api_key = "********************************************************************************************************************************************************************" #os.getenv("OPENAI_API_KEY", "your-openai-api-key")

# Database setup (SQLite for simplicity)
DATABASE_URL = "sqlite:///./chat.db"
engine = create_engine(DATABASE_URL, connect_args={"check_same_thread": False})
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# SQLAlchemy model for storing chat messages
class ChatMessage(Base):
    __tablename__ = "chat_messages"
    id = Column(Integer, primary_key=True, index=True)
    user_message = Column(Text, nullable=False)
    bot_response = Column(Text, nullable=False)
    timestamp = Column(DateTime, default=datetime.utcnow)

# Create the database table
Base.metadata.create_all(bind=engine)

# FastAPI app initialization
app = FastAPI()

# Pydantic model for request body
class MessageRequest(BaseModel):
    message: str

# Pydantic model for response
class MessageResponse(BaseModel):
    response: str

@app.post("/chat", response_model=MessageResponse)
def chat_with_bot(request: MessageRequest):
    """
    Receives a user message, gets a response from OpenAI, stores both in the database, and returns the response.
    """
    user_message = request.message
    # Call OpenAI API to get a chatbot response
    try:
        completion = openai.ChatCompletion.create(
            model="gpt-3.5-turbo",
            messages=[{"role": "user", "content": user_message}]
        )
        bot_response = completion.choices[0].message["content"].strip()
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"OpenAI API error: {e}")

    # Store the chat in the database
    db = SessionLocal()
    chat = ChatMessage(user_message=user_message, bot_response=bot_response)
    db.add(chat)
    db.commit()
    db.refresh(chat)
    db.close()

    return MessageResponse(response=bot_response)

@app.get("/history")
def get_chat_history():
    """
    Returns the full chat history from the database.
    """
    db = SessionLocal()
    chats = db.query(ChatMessage).order_by(ChatMessage.timestamp).all()
    db.close()
    # Return as a list of dicts
    return [
        {
            "id": chat.id,
            "user_message": chat.user_message,
            "bot_response": chat.bot_response,
            "timestamp": chat.timestamp
        }
        for chat in chats
    ]

# To run the server: uvicorn chatbot:app --reload
# Make sure to set your OPENAI_API_KEY environment variable or replace it in the code above.
