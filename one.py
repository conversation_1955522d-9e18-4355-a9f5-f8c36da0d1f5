# import fastapi 
from fastapi import FastAPI
from request import send_request
from pydantic import BaseModel
from enum import Enum

class Products(BaseModel):
    id: int
    name: str
    des: str | None = None

class Model_Name(str, Enum):
    base1 = "model1"
    base2 = "model2"
    base3 = "model3"

app = FastAPI()
products= []
@app.get("/")
def index():
    return {"e2":"okay"}

@app.put("/")
def  aura():
    return {"hii":"how was the day.",
            1: 101.3,
            'okay': "not ookay",
            3: [1.3,4,6,7]
            } 

@app.put("/end1")
def this_function( age: int, name:str = 'lala'):
    # send_request(
    # url = "https://http://127.0.0.1:8000/end1")
    return f"thanks for your name, {name}, {age}"

@app.get("/getprod")
def getprod(id: int ):
    return products[id]


@app.get("/getprodall")
def getprodall():
    return products

@app.post("/addprod")
def addprod(product: Products):
    products.append(product)
    print(products)
    return product

@app.get("/byId")
def getById(id: int ):
    for i in products:
        if i.id == id:
            return i
    return {"not":"found"}

@app.get("/modelSelection/{model_name}")
def modelName(model_name : Model_Name):
    if model_name is Model_Name.base1:
        return {"model Name": "Base1", "API": "ugf91fb0r9h0"}
    return ['other models are used']