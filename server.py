from fastapi import FastAP<PERSON>
from pydantic import BaseModel
from openai import OpenAI
from typing import List, Dict, Any

app = FastAPI()
# List of messages, each with a role and content, as required by OpenAI API
messages: List[Any] = [
    {"role": "system", "content": "Behave like a baby chatbot. who is super genius"}
]

def send_chat():
    api_key = "********************************************************************************************************************************************************************"
    client = OpenAI(api_key=api_key)
    completion = client.chat.completions.create(
        model="gpt-3.5-turbo",
        messages=messages
    )
    # Print the assistant's reply
    content = completion.choices[0].message.content or ""
    print(content)
    # Append the assistant's reply to the messages list, ensuring content is a string
    messages.append({"role": "assistant", "content": content})
    return content

@app.get("/chat")
def newMessage(new_message: str):
    # Add the user's message to the conversation
    messages.append({"role": "user", "content": new_message})
    # Get the assistant's reply
    assistant_reply = send_chat()
    # Return the assistant's reply
    return {"role": "assistant", "content": assistant_reply}

@app.put("/history")
def allChat():
    return messages